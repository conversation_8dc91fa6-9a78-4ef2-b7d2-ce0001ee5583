import React from "react";
import { Icon } from "@iconify/react";

const Footer: React.FC = () => {
  return (
    <footer className="bg-primary">
      <div className="max-w-[1500px] flex flex-col md:flex-row justify-between mx-auto pt-12 md:pt-16 lg:pt-20 pb-16 md:pb-20 lg:pb-24 px-4 lg:px-6">
        {/* Logo Section */}
        <div className="md:col-span-1 mb-8 md:mb-0">
          <div className="flex items-center mb-4">
            <div className="w-full max-w-[40px] md:max-w-[50px] mr-3">
              <img src="/zybra_logo_white.png" alt="Zybra Logo White" className="w-full h-auto" />
            </div>
            <span className="text-white text-xl md:text-2xl font-medium">Zybra</span>
          </div>
        </div>

        <div className="text-lg md:text-xl lg:text-[22px] flex flex-col md:flex-row gap-8 md:gap-12 lg:gap-16 xl:gap-20">
          {/* About Zybra */}
          <div className="md:col-span-1">
            <ul className="space-y-3 md:space-y-4">
              <li>
                <a
                  href="#"
                  className="text-white/80 hover:text-white transition-colors font-light"
                >
                  About Zybra
                </a>
              </li>
              <li>
                <a
                  href="#"
                  className="text-white/80 hover:text-white transition-colors font-light"
                >
                  Terms & Conditions
                </a>
              </li>
            </ul>
          </div>

          {/* Privacy Policy */}
          <div className="md:col-span-1">
            <ul className="space-y-3 md:space-y-4">
              <li>
                <a
                  href="#"
                  className="text-white/80 hover:text-white transition-colors font-light"
                >
                  Privacy Policy
                </a>
              </li>
              <li>
                <a
                  href="#"
                  className="text-white/80 hover:text-white transition-colors font-light"
                >
                  Contact Us
                </a>
              </li>
            </ul>
          </div>

          {/* Social */}
          <div className="md:col-span-1">
            <h3 className="text-white/80 mb-4 font-medium">Socials</h3>
            <div className="flex space-x-3 md:space-x-4">
              {/* Social Media Icons */}
              <a
                href="#"
                className="text-white/80 hover:text-white transition-colors"
              >
                <Icon
                  icon="ant-design:twitter-circle-filled"
                  className="w-6 h-6 md:w-[26px] md:h-[26px]"
                />
              </a>
              <a
                href="#"
                className="text-white/80 hover:text-white transition-colors"
              >
                <Icon
                  icon="ant-design:linkedin-filled"
                  className="w-6 h-6 md:w-[26px] md:h-[26px]"
                />
              </a>
              <a
                href="#"
                className="text-white/80 hover:text-white transition-colors"
              >
                <Icon
                  icon="ant-design:github-filled"
                  className="w-6 h-6 md:w-[26px] md:h-[26px]"
                />
              </a>
              <a
                href="#"
                className="text-white/80 hover:text-white transition-colors"
              >
                <Icon
                  icon="ic:baseline-discord"
                  className="w-6 h-6 md:w-[26px] md:h-[26px]"
                />
              </a>
            </div>
          </div>
        </div>
      </div>

      {/* Zybra Pattern/Logo */}
      <div className="w-full max-w-lg md:max-w-xl lg:max-w-2xl xl:max-w-3xl 2xl:max-w-4xl mx-auto px-4 pb-8 md:pb-12 lg:pb-16">
        <img
          src="/zybra.png"
          alt="Zybra Pattern"
          className="w-full h-auto object-contain"
        />
      </div>
    </footer>
  );
};

export default Footer;
