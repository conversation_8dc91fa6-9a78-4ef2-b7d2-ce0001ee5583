import React from "react";
import { Icon } from "@iconify/react";

const Footer: React.FC = () => {
  return (
    <footer className="bg-primary">
      <div className="max-w-6xl lg:max-w-7xl xl:max-w-8xl 2xl:max-w-9xl flex flex-col md:flex-row justify-between mx-auto pt-12 md:pt-16 lg:pt-20 xl:pt-24 2xl:pt-28 pb-16 md:pb-20 lg:pb-24 xl:pb-28 2xl:pb-32 px-4 lg:px-6 xl:px-8">
        {/* Logo Section */}
        <div className="md:col-span-1 mb-8 md:mb-0">
          <div className="flex items-center mb-4 lg:mb-6">
            <div className="w-full max-w-[40px] md:max-w-[50px] lg:max-w-[60px] xl:max-w-[70px] mr-3 lg:mr-4">
              <img src="/zybra_logo_white.png" alt="Zybra Logo White" className="w-full h-auto" />
            </div>
            <span className="text-white text-xl md:text-2xl lg:text-3xl xl:text-4xl 2xl:text-5xl font-medium">Zybra</span>
          </div>
        </div>

        <div className="text-lg md:text-xl lg:text-2xl xl:text-3xl 2xl:text-4xl flex flex-col md:flex-row gap-8 md:gap-12 lg:gap-16 xl:gap-20 2xl:gap-24">
          {/* About Zybra */}
          <div className="md:col-span-1">
            <ul className="space-y-3 md:space-y-4 lg:space-y-5 xl:space-y-6">
              <li>
                <a
                  href="#"
                  className="text-white/80 hover:text-white transition-colors font-light"
                >
                  About Zybra
                </a>
              </li>
              <li>
                <a
                  href="#"
                  className="text-white/80 hover:text-white transition-colors font-light"
                >
                  Terms & Conditions
                </a>
              </li>
            </ul>
          </div>

          {/* Privacy Policy */}
          <div className="md:col-span-1">
            <ul className="space-y-3 md:space-y-4 lg:space-y-5 xl:space-y-6">
              <li>
                <a
                  href="#"
                  className="text-white/80 hover:text-white transition-colors font-light"
                >
                  Privacy Policy
                </a>
              </li>
              <li>
                <a
                  href="#"
                  className="text-white/80 hover:text-white transition-colors font-light"
                >
                  Contact Us
                </a>
              </li>
            </ul>
          </div>

          {/* Social */}
          <div className="md:col-span-1">
            <h3 className="text-white/80 mb-4 lg:mb-6 xl:mb-8 font-medium">Socials</h3>
            <div className="flex space-x-3 md:space-x-4 lg:space-x-5 xl:space-x-6">
              {/* Social Media Icons */}
              <a
                href="#"
                className="text-white/80 hover:text-white transition-colors"
              >
                <Icon
                  icon="ant-design:twitter-circle-filled"
                  className="w-6 h-6 md:w-7 md:h-7 lg:w-8 lg:h-8 xl:w-9 xl:h-9 2xl:w-10 2xl:h-10"
                />
              </a>
              <a
                href="#"
                className="text-white/80 hover:text-white transition-colors"
              >
                <Icon
                  icon="ant-design:linkedin-filled"
                  className="w-6 h-6 md:w-7 md:h-7 lg:w-8 lg:h-8 xl:w-9 xl:h-9 2xl:w-10 2xl:h-10"
                />
              </a>
              <a
                href="#"
                className="text-white/80 hover:text-white transition-colors"
              >
                <Icon
                  icon="ant-design:github-filled"
                  className="w-6 h-6 md:w-7 md:h-7 lg:w-8 lg:h-8 xl:w-9 xl:h-9 2xl:w-10 2xl:h-10"
                />
              </a>
              <a
                href="#"
                className="text-white/80 hover:text-white transition-colors"
              >
                <Icon
                  icon="ic:baseline-discord"
                  className="w-6 h-6 md:w-7 md:h-7 lg:w-8 lg:h-8 xl:w-9 xl:h-9 2xl:w-10 2xl:h-10"
                />
              </a>
            </div>
          </div>
        </div>
      </div>

      {/* Zybra Pattern/Logo */}
      <div className="w-full max-w-lg md:max-w-xl lg:max-w-2xl xl:max-w-3xl 2xl:max-w-4xl mx-auto px-4 pb-8 md:pb-12 lg:pb-16">
        <img
          src="/zybra.png"
          alt="Zybra Pattern"
          className="w-full h-auto object-contain"
        />
      </div>
    </footer>
  );
};

export default Footer;
