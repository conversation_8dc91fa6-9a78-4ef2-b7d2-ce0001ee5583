import React from "react";

const CallToActionSection: React.FC = () => {
  return (
    <section className="bg-primary pt-16 md:pt-20 lg:pt-24 xl:pt-28 2xl:pt-32 3xl:pt-40 pb-12 md:pb-16 lg:pb-20 xl:pb-24 2xl:pb-28 3xl:pb-32">
      <div className="mx-auto max-w-7xl xl:max-w-8xl 2xl:max-w-9xl 3xl:max-w-10xl px-4 lg:px-6 xl:px-8">
        <div className="flex flex-col lg:flex-row items-center justify-between gap-6 md:gap-8 lg:gap-12 xl:gap-16 2xl:gap-20">
          {/* Left Content */}
          <div className="text-white flex w-full lg:w-1/2 justify-center lg:justify-start">
            <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl 2xl:text-7xl 3xl:text-8xl leading-tight lg:leading-[1.1] text-center lg:text-left font-bold">
              Ready to Open Your <br />
              Digital Dollar Account?
            </h2>
          </div>

          {/* Right Content - Call to Action Card */}
          <div className="w-full lg:w-1/2 lg:max-w-none flex items-center bg-white rounded-2xl lg:rounded-l-3xl xl:rounded-l-4xl lg:rounded-r-none p-6 md:p-8 lg:p-10 xl:p-12 2xl:p-16 3xl:p-20 min-h-[200px] md:min-h-[250px] lg:min-h-[300px] xl:min-h-[350px] 2xl:min-h-[400px] 3xl:min-h-[450px]">
            <div className="text-lg sm:text-xl md:text-2xl lg:text-3xl xl:text-4xl 2xl:text-5xl 3xl:text-6xl text-gray-800 leading-relaxed">
              <p className="mb-2 lg:mb-4">
                Dial <span className="font-bold text-primary">*334#</span> or{" "}
                <span className="text-primary font-semibold underline cursor-pointer hover:text-primary/80 transition-colors">
                  download
                </span>{" "}
                the Zybra
              </p>
              <p className="mb-2 lg:mb-4">app today. Start saving in real U.S.</p>
              <p>dollars now.</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CallToActionSection;
