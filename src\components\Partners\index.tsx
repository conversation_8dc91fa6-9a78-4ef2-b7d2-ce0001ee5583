"use client";

import React, { useEffect, useRef } from "react";

const TrustedByCarousel = () => {
  const carouselRef = useRef<HTMLDivElement>(null);

  const companies = [
    { name: "Yellow Card", logo: "/yellow_card_logo.png" },
    { name: "Kotani Pay", logo: "/kotani_pay_logo.png" },
    { name: "Chainlink", logo: "/chainlink_logo.png" },
    { name: "Swarm", logo: "/swarm_logo.png" },
    { name: "Centrifuge", logo: "/centrifuge_logo.png" },
  ];

  useEffect(() => {
    const carousel = carouselRef.current;
    if (!carousel) return;

    let animationId: number | undefined;

    interface Company {
      name: string;
      logo: string;
    }
    let scrollPosition = 0;
    const scrollSpeed = 0.5;

    const animate = () => {
      scrollPosition += scrollSpeed;

      if (scrollPosition >= carousel.scrollWidth / 2) {
        scrollPosition = 0;
      }

      carousel.scrollLeft = scrollPosition;
      animationId = requestAnimationFrame(animate);
    };

    animationId = requestAnimationFrame(animate);

    const handleMouseEnter = () => {
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    };

    const handleMouseLeave = () => {
      animationId = requestAnimationFrame(animate);
    };

    carousel.addEventListener("mouseenter", handleMouseEnter);
    carousel.addEventListener("mouseleave", handleMouseLeave);

    return () => {
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
      carousel.removeEventListener("mouseenter", handleMouseEnter);
      carousel.removeEventListener("mouseleave", handleMouseLeave);
    };
  }, []);

  return (
    <section className="bg-primary py-12 md:py-16 lg:py-20 xl:py-24 2xl:py-28 3xl:py-32 overflow-hidden pt-32 md:pt-40 lg:pt-48 xl:pt-52 2xl:pt-56 3xl:pt-60 pb-24 md:pb-32 lg:pb-36 xl:pb-40 2xl:pb-44 3xl:pb-48">
      <div className="flex flex-col md:flex-row gap-6 md:gap-8 lg:gap-12 xl:gap-16 2xl:gap-20 max-w-6xl lg:max-w-7xl xl:max-w-8xl 2xl:max-w-9xl 3xl:max-w-10xl mx-auto px-4 lg:px-6 xl:px-8">
        <div className="flex items-center text-white gap-3 md:gap-4 lg:gap-6 xl:gap-8 flex-shrink-0 justify-center md:justify-start">
          <svg
            width="auto"
            height="auto"
            viewBox="0 0 38 39"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="w-8 md:w-10 lg:w-12 xl:w-14 2xl:w-16 3xl:w-18"
          >
            <path
              d="M6.15039 9.40763L18.9205 5.30859V33.6866C9.79854 29.9028 6.15039 22.6507 6.15039 18.5516V9.40763ZM31.6906 9.40763L18.9205 5.30859V33.6866C28.0424 29.9028 31.6906 22.6507 31.6906 18.5516V9.40763Z"
              fill="#EAEAEA"
            />
          </svg>
          <span className="text-xl md:text-2xl lg:text-3xl xl:text-4xl 2xl:text-5xl 3xl:text-6xl whitespace-nowrap font-medium">Trusted by</span>
        </div>

        <div className="relative overflow-hidden flex-1 border-l-0 md:border-l-[0.32px] border-r-0 md:border-r-[0.32px] border-[#EAEAEA] rounded-lg md:rounded-none">
          <div
            ref={carouselRef}
            className="flex overflow-hidden"
            style={{ scrollBehavior: "unset" }}
          >
            {companies.map((company, index) => (
              <div
                key={`first-${index}`}
                className="flex items-center justify-center min-w-[150px] md:min-w-[180px] lg:min-w-[200px] xl:min-w-[240px] 2xl:min-w-[280px] 3xl:min-w-[320px] px-6 md:px-8 lg:px-10 xl:px-12 2xl:px-14 3xl:px-16 flex-shrink-0"
              >
                <img
                  src={company.logo}
                  alt={company.name}
                  className="max-h-12 md:max-h-14 lg:max-h-16 xl:max-h-18 2xl:max-h-20 3xl:max-h-24 w-auto opacity-90 hover:opacity-100 transition-opacity duration-300"
                />
              </div>
            ))}
            {companies.map((company, index) => (
              <div
                key={`second-${index}`}
                className="flex items-center justify-center min-w-[150px] md:min-w-[180px] lg:min-w-[200px] xl:min-w-[240px] 2xl:min-w-[280px] 3xl:min-w-[320px] px-6 md:px-8 lg:px-10 xl:px-12 2xl:px-14 3xl:px-16 flex-shrink-0"
              >
                <img
                  src={company.logo}
                  alt={company.name}
                  className="max-h-12 md:max-h-14 lg:max-h-16 xl:max-h-18 2xl:max-h-20 3xl:max-h-24 w-auto opacity-90 hover:opacity-100 transition-opacity duration-300"
                />
              </div>
            ))}
          </div>

          <div className="absolute top-0 left-0 w-6 md:w-8 lg:w-10 xl:w-12 h-full pointer-events-none z-10 bg-gradient-to-r from-primary to-transparent"></div>
          <div className="absolute top-0 right-0 w-6 md:w-8 lg:w-10 xl:w-12 h-full pointer-events-none z-10 bg-gradient-to-l from-primary to-transparent"></div>
        </div>
      </div>
    </section>
  );
};

export default TrustedByCarousel;
