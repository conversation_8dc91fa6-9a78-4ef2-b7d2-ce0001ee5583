import React from "react";

const CardPreviewSection: React.FC = () => {
  return (
    <section className="bg-gray-50 py-16 md:py-20 lg:py-24 xl:py-32 2xl:py-40 3xl:py-48 px-4">
      <div className="max-w-4xl lg:max-w-5xl xl:max-w-6xl 2xl:max-w-7xl mx-auto text-center">
        {/* Card Image */}
        <div className="w-full max-w-xl lg:max-w-2xl xl:max-w-3xl 2xl:max-w-4xl mx-auto mb-8 md:mb-12 lg:mb-16 xl:mb-20">
          <img
            src="/card.png"
            alt="Zybra Card Preview"
            className="w-full h-auto object-contain"
          />
        </div>

        {/* Text Content */}
        <h2 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl 2xl:text-8xl 3xl:text-9xl font-bold text-gray-900 leading-tight">
          3D for this in the works
        </h2>
      </div>
    </section>
  );
};

export default CardPreviewSection;
