import React from "react";
import Image from "next/image";

const AccessibleAnywhereSection: React.FC = () => {
  return (
    <section className="py-16 md:py-20 lg:py-24 xl:py-28 2xl:py-32 px-4 bg-gray-50">
      <div className="max-w-7xl xl:max-w-8xl 2xl:max-w-9xl mx-auto">
        {/* Header */}
        <div className="text-center mb-12 md:mb-16 lg:mb-20 xl:mb-24 2xl:mb-28">
          <h2 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl 2xl:text-8xl 3xl:text-9xl font-bold text-[#021018] mb-4 md:mb-6 lg:mb-8">
            Accessible Anywhere
          </h2>
          <p className="text-lg sm:text-xl md:text-2xl lg:text-3xl xl:text-4xl 2xl:text-5xl 3xl:text-6xl text-[#021018] font-light">
            Access your mobile money anywhere.
          </p>
        </div>

        {/* Feature Cards Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 md:gap-8 lg:gap-10 xl:gap-12 2xl:gap-16 px-0 sm:px-4 md:px-8 lg:px-12 xl:px-16 2xl:px-20">
          {/* Mobile App Card */}
          <div className="aspect-square bg-primary rounded-2xl lg:rounded-3xl xl:rounded-4xl p-6 md:p-8 lg:p-10 xl:p-12 2xl:p-16 text-white relative overflow-hidden group hover:scale-105 transition-transform duration-300">
            <div className="relative z-10">
              <h3 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl 2xl:text-7xl font-bold mb-2 md:mb-4">Mobile App</h3>
              <p className="text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl 2xl:text-6xl font-light">IOS & Android</p>
            </div>
            <div className="flex justify-center absolute bottom-0 left-0 w-full h-3/5">
              <img
                src="/ussd-card-mockup.png"
                alt="Mobile app interface"
                className="w-full h-full object-contain object-bottom"
              />
            </div>
          </div>

          {/* USSD Card */}
          <div className="aspect-square bg-primary rounded-2xl lg:rounded-3xl xl:rounded-4xl p-6 md:p-8 lg:p-10 xl:p-12 2xl:p-16 text-white relative overflow-hidden group hover:scale-105 transition-transform duration-300">
            <div className="relative z-10">
              <h3 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl 2xl:text-7xl font-bold mb-2 md:mb-4">USSD</h3>
              <p className="text-lg sm:text-xl md:text-2xl lg:text-3xl xl:text-4xl 2xl:text-5xl font-light leading-tight">
                Works on any phone, <br /> no internet required
              </p>
            </div>
            <div className="flex justify-center absolute bottom-0 left-0 w-full h-3/5">
              <img
                src="/keypad-phone-mockup.png"
                alt="USSD interface"
                className="w-full h-full object-contain object-bottom"
              />
            </div>
          </div>

          {/* Web Dashboard Card */}
          <div className="aspect-square bg-primary rounded-2xl lg:rounded-3xl xl:rounded-4xl p-6 md:p-8 lg:p-10 xl:p-12 2xl:p-16 text-white relative overflow-hidden group hover:scale-105 transition-transform duration-300 md:col-span-2 xl:col-span-1">
            <div className="relative z-10">
              <h3 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl 2xl:text-7xl font-bold mb-2 md:mb-4">Web Dashboard</h3>
              <p className="text-lg sm:text-xl md:text-2xl lg:text-3xl xl:text-4xl 2xl:text-5xl font-light leading-tight">
                Track and manage
                <br /> your account online
              </p>
            </div>
            <div className="flex justify-center absolute bottom-0 left-0 w-full h-3/5">
              <img
                src="/dashboard-mockup.png"
                alt="Web dashboard interface"
                className="w-full h-full object-contain object-bottom"
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AccessibleAnywhereSection;
