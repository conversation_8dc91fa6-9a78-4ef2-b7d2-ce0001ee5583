import type { Config } from "tailwindcss";

const config: Config = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: "#104E47", // added custom color
      },
      backgroundImage: {
        "gradient-radial": "radial-gradient(var(--tw-gradient-stops))",
        "gradient-conic":
          "conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))",
      },
      screens: {
        'xs': '475px',
        'sm': '640px',
        'md': '768px',
        'lg': '1024px',
        'xl': '1280px',
        '2xl': '1536px',
        '3xl': '1920px',
        '4xl': '2560px',
      },
      fontSize: {
        // Enhanced font sizes for better desktop scaling
        'xs': ['0.75rem', { lineHeight: '1rem' }],
        'sm': ['0.875rem', { lineHeight: '1.25rem' }],
        'base': ['1rem', { lineHeight: '1.5rem' }],
        'lg': ['1.125rem', { lineHeight: '1.75rem' }],
        'xl': ['1.25rem', { lineHeight: '1.75rem' }],
        '2xl': ['1.5rem', { lineHeight: '2rem' }],
        '3xl': ['1.875rem', { lineHeight: '2.25rem' }],
        '4xl': ['2.25rem', { lineHeight: '2.5rem' }],
        '5xl': ['3rem', { lineHeight: '1' }],
        '6xl': ['3.75rem', { lineHeight: '1' }],
        '7xl': ['4.5rem', { lineHeight: '1' }],
        '8xl': ['6rem', { lineHeight: '1' }],
        '9xl': ['8rem', { lineHeight: '1' }],
        // Custom desktop-optimized sizes
        'desktop-sm': ['1.125rem', { lineHeight: '1.6rem' }],
        'desktop-base': ['1.25rem', { lineHeight: '1.8rem' }],
        'desktop-lg': ['1.5rem', { lineHeight: '2.1rem' }],
        'desktop-xl': ['1.875rem', { lineHeight: '2.4rem' }],
        'desktop-2xl': ['2.25rem', { lineHeight: '2.7rem' }],
        'desktop-3xl': ['2.875rem', { lineHeight: '3.2rem' }],
        'desktop-4xl': ['3.5rem', { lineHeight: '3.8rem' }],
        'desktop-5xl': ['4.5rem', { lineHeight: '4.8rem' }],
        'desktop-6xl': ['5.5rem', { lineHeight: '5.8rem' }],
        'desktop-7xl': ['6.5rem', { lineHeight: '6.8rem' }],
        'desktop-8xl': ['8rem', { lineHeight: '8.2rem' }],
      },
      spacing: {
        // Enhanced spacing for desktop layouts
        '18': '4.5rem',
        '22': '5.5rem',
        '26': '6.5rem',
        '30': '7.5rem',
        '34': '8.5rem',
        '38': '9.5rem',
        '42': '10.5rem',
        '46': '11.5rem',
        '50': '12.5rem',
        '54': '13.5rem',
        '58': '14.5rem',
        '62': '15.5rem',
        '66': '16.5rem',
        '70': '17.5rem',
        '74': '18.5rem',
        '78': '19.5rem',
        '82': '20.5rem',
        '86': '21.5rem',
        '90': '22.5rem',
        '94': '23.5rem',
        '98': '24.5rem',
        '102': '25.5rem',
        '106': '26.5rem',
        '110': '27.5rem',
        '114': '28.5rem',
        '118': '29.5rem',
        '122': '30.5rem',
        '126': '31.5rem',
        '130': '32.5rem',
      },
      maxWidth: {
        '8xl': '88rem',
        '9xl': '96rem',
        '10xl': '104rem',
        '11xl': '112rem',
        '12xl': '120rem',
      },
    },
  },
  plugins: [],
};
export default config;
