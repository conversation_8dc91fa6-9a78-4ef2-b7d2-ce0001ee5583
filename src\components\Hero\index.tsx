export default function LandingPage() {
  return (
    <div
      className="flex flex-col min-h-screen"
      style={{
        background: "linear-gradient(135deg, #D9F1EA, #AEFFE8)",
      }}
    >
      <nav className="w-full absolute flex items-center justify-between px-4 sm:px-6 md:px-10 lg:px-16 xl:px-24 2xl:px-30 3xl:px-38 py-4 sm:py-6 md:py-8 lg:py-10 xl:py-12 z-10">
        <div className="flex items-center gap-4 sm:gap-8 md:gap-12 lg:gap-16 xl:gap-20 2xl:gap-24 3xl:gap-30">
          <div className="w-[25px] sm:w-[30px] md:w-[40px] lg:w-[50px] xl:w-[60px] 2xl:w-[70px] 3xl:w-[80px]">
            <img
              src="/zybra_logo_black.png"
              alt="Zybra Logo Black"
              className="w-full h-auto"
            />
          </div>
          <div className="hidden md:flex items-center gap-4 lg:gap-6 xl:gap-8 2xl:gap-10 3xl:gap-12">
            <a
              href="#"
              className="text-[#021018] text-sm md:text-base lg:text-lg xl:text-xl 2xl:text-2xl 3xl:text-3xl hover:opacity-70 transition-opacity font-medium"
            >
              Docs
            </a>
            <a
              href="#"
              className="text-[#021018] text-sm md:text-base lg:text-lg xl:text-xl 2xl:text-2xl 3xl:text-3xl hover:opacity-70 transition-opacity font-medium"
            >
              Faq
            </a>
          </div>
        </div>
        <button className="bg-[#021018] text-white px-3 py-2 sm:px-4 sm:py-2.5 md:px-6 md:py-3 lg:px-8 lg:py-4 xl:px-10 xl:py-5 2xl:px-12 2xl:py-6 3xl:px-14 3xl:py-7 rounded-lg md:rounded-xl lg:rounded-2xl text-xs sm:text-sm md:text-base lg:text-lg xl:text-xl 2xl:text-2xl 3xl:text-3xl flex items-center gap-1 sm:gap-2 lg:gap-3 xl:gap-4 hover:bg-gray-800 transition-colors font-medium">
          <span className="hidden sm:inline">Download App</span>
          <span className="sm:hidden">App</span>
          <svg
            width="18"
            height="17"
            viewBox="0 0 22 21"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="w-3 h-3 sm:w-4 sm:h-4 md:w-5 md:h-5 lg:w-6 lg:h-6 xl:w-7 xl:h-7 2xl:w-8 2xl:h-8"
          >
            <path
              d="M11.0191 13.7638L6.8049 9.54956L7.98488 8.32744L10.1763 10.5188V3.64966H11.862V10.5188L14.0533 8.32744L15.2333 9.54956L11.0191 13.7638ZM5.96205 17.1352C5.49849 17.1352 5.10179 16.9702 4.77196 16.6404C4.44213 16.3106 4.27693 15.9136 4.27637 15.4495V12.9209H5.96205V15.4495H16.0762V12.9209H17.7619V15.4495C17.7619 15.913 17.5969 16.31 17.2671 16.6404C16.9373 16.9708 16.5403 17.1357 16.0762 17.1352H5.96205Z"
              fill="white"
            />
          </svg>
        </button>
      </nav>

      <div className="w-full h-full flex flex-1 flex-col justify-center">
        <div className="flex flex-col justify-center px-4 sm:px-6 md:px-10 lg:px-16 xl:px-24 2xl:px-30 3xl:px-38 pt-20 sm:pt-24 md:pt-28 lg:pt-32 xl:pt-36 2xl:pt-40">
          <h1 className="text-2xl sm:text-3xl md:text-5xl lg:text-6xl xl:text-7xl 2xl:text-8xl 3xl:text-9xl text-[#021018] font-normal leading-[0.9] mb-3 sm:mb-4 md:mb-6 lg:mb-8 xl:mb-10 2xl:mb-12 text-balance">
            Virtual Dollar
            <br />
            Savings Account
          </h1>

          <p className="text-lg sm:text-xl md:text-2xl lg:text-3xl xl:text-4xl 2xl:text-5xl 3xl:text-6xl text-[#021018] font-light mb-4 sm:mb-6 md:mb-8 lg:mb-10 xl:mb-12 2xl:mb-16 text-balance">
            No Bank, No Internet Needed.
          </p>

          <p className="text-sm sm:text-base md:text-lg lg:text-xl xl:text-2xl 2xl:text-3xl 3xl:text-4xl text-gray-700 font-light leading-[1.3] mb-6 sm:mb-8 md:mb-10 lg:mb-12 xl:mb-16 2xl:mb-20 text-pretty max-w-4xl xl:max-w-5xl 2xl:max-w-6xl">
            Save and Grow Real U.S. Dollars up to 10%.
            <br className="hidden sm:block" />
            <span className="sm:hidden"> </span>
            All From Your Mobile Money!
          </p>

          <div className="flex flex-col sm:flex-row flex-wrap gap-3 sm:gap-4 lg:gap-6 xl:gap-8 mb-4 sm:mb-6 lg:mb-8 xl:mb-10">
            <button className="bg-[#021018] text-white px-4 sm:px-6 md:px-8 lg:px-10 xl:px-12 2xl:px-16 py-3 sm:py-4 md:py-5 lg:py-6 xl:py-7 2xl:py-8 rounded-xl sm:rounded-2xl lg:rounded-3xl flex items-center justify-center gap-2 sm:gap-3 lg:gap-4 xl:gap-5 text-sm sm:text-base md:text-lg lg:text-xl xl:text-2xl 2xl:text-3xl 3xl:text-4xl w-full sm:w-auto hover:bg-gray-800 transition-colors font-medium">
              <svg
                width="16"
                height="21"
                viewBox="0 0 16 21"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                className="w-3 h-4 sm:w-4 sm:h-5 md:w-5 md:h-6 lg:w-6 lg:h-7 xl:w-7 xl:h-8 2xl:w-8 2xl:h-9"
              >
                <path
                  d="M3.21575 8.28209C3.88246 11.0974 5.54608 13.6638 7.93384 15.5971L10.662 14.1872C10.9968 14.0141 11.4116 14.0292 11.7224 14.2163C12.7208 14.8029 13.8518 15.2619 15.0645 15.5527C15.6024 15.6818 15.9246 16.1813 15.7804 16.6627L14.8659 19.718C14.7217 20.1995 14.1637 20.4879 13.6258 20.3589C4.4424 18.1563 -1.00595 9.70908 1.45474 1.48872C1.59887 1.00723 2.1569 0.718839 2.6948 0.847854L6.1178 1.66886C6.6557 1.79787 6.97787 2.29738 6.83374 2.77887C6.50617 3.87317 6.38731 4.9706 6.45567 6.03789C6.47153 6.37009 6.29109 6.69275 5.94387 6.87219L3.21575 8.28209Z"
                  fill="#F9FFFE"
                />
              </svg>
              Dial *334#
            </button>

            <button className="backdrop-blur-sm border-2 border-black text-black px-4 sm:px-6 md:px-8 lg:px-10 xl:px-12 2xl:px-16 py-3 sm:py-4 md:py-5 lg:py-6 xl:py-7 2xl:py-8 rounded-xl sm:rounded-2xl lg:rounded-3xl flex items-center justify-center gap-2 sm:gap-3 lg:gap-4 xl:gap-5 text-sm sm:text-base md:text-lg lg:text-xl xl:text-2xl 2xl:text-3xl 3xl:text-4xl w-full sm:w-auto hover:bg-black/5 transition-colors font-medium">
              Download App
              <svg
                width="16"
                height="17"
                viewBox="0 0 16 17"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                className="w-3 h-3 sm:w-4 sm:h-4 md:w-5 md:h-5 lg:w-6 lg:h-6 xl:w-7 xl:h-7 2xl:w-8 2xl:h-8"
              >
                <path
                  d="M8.18458 12.4048L3.40003 7.62025L4.7397 6.23273L7.22767 8.72069V0.921875H9.14149V8.72069L11.6295 6.23273L12.9691 7.62025L8.18458 12.4048ZM2.44312 16.2324C1.91682 16.2324 1.46643 16.0452 1.09196 15.6707C0.717489 15.2963 0.529935 14.8456 0.529297 14.3186V11.4479H2.44312V14.3186H13.926V11.4479H15.8399V14.3186C15.8399 14.8449 15.6526 15.2956 15.2782 15.6707C14.9037 16.0458 14.453 16.2331 13.926 16.2324H2.44312Z"
                  fill="#021018"
                />
              </svg>
            </button>
          </div>

          <p className="text-[#3E535F] text-xs sm:text-sm md:text-base lg:text-lg xl:text-xl 2xl:text-2xl 3xl:text-3xl text-pretty max-w-3xl xl:max-w-4xl 2xl:max-w-5xl">
            Dial *334# or download the Zybra app to open your digital dollar
            account now.
          </p>
        </div>
      </div>
    </div>
  );
}
