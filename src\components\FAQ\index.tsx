"use client";

import React, { useState } from "react";

interface FAQItem {
  id: number;
  question: string;
  answer?: string;
}

const FAQSection: React.FC = () => {
  const [openItem, setOpenItem] = useState<number | null>(2);

  const faqItems: FAQItem[] = [
    {
      id: 1,
      question: "Is Zybra a bank?",
      answer:
        "No, Zybra is not crypto. We use secure global distributed financial technology to give you a digital dollar account — a simple, virtual way to save and grow real U.S. dollars.",
    },
    {
      id: 2,
      question: "Is this a crypto app?",
      answer:
        "No, Zybra is not crypto. We use secure global distributed financial technology to give you a digital dollar account — a simple, virtual way to save and grow real U.S. dollars.",
    },
    {
      id: 3,
      question: "Where is my money allocated?",
      answer:
        "Your money is allocated in secure, regulated financial institutions that provide the infrastructure for your digital dollar account.",
    },
    {
      id: 4,
      question: "Can I lose money?",
      answer:
        "While we take extensive measures to protect your funds, no investment is without risk. It's important to understand the risks involved in any financial product.",
    },
    {
      id: 5,
      question: "How do I start?",
      answer:
        "Getting started with <PERSON>y<PERSON> is easy! Simply sign up for an account, complete the verification process, and you can begin saving and growing your digital dollars.",
    },
  ];

  const toggleItem = (id: number) => {
    setOpenItem(openItem === id ? null : id);
  };

  return (
    <section className="bg-white py-16 md:py-20 lg:py-24 xl:py-28 2xl:py-32 3xl:py-40 px-4">
      <div className="max-w-4xl lg:max-w-5xl xl:max-w-6xl 2xl:max-w-7xl 3xl:max-w-8xl mx-auto">
        {/* Header */}
        <h2 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl 2xl:text-8xl 3xl:text-9xl font-bold text-[#021018] text-center mb-8 md:mb-12 lg:mb-16 xl:mb-20 2xl:mb-24">
          Frequently asked questions
        </h2>

        {/* FAQ Items */}
        <div className="space-y-3 md:space-y-4 lg:space-y-6 xl:space-y-8">
          {faqItems.map((item) => (
            <div
              key={item.id}
              className={`border border-[#E7EBEE] rounded-xl md:rounded-2xl lg:rounded-3xl overflow-hidden transition-all duration-300 hover:shadow-lg ${
                openItem === item.id ? "bg-white shadow-md" : "bg-[#FAFAFA]"
              }`}
            >
              <button
                onClick={() => toggleItem(item.id)}
                className="w-full flex items-start gap-4 sm:gap-6 md:gap-8 lg:gap-10 xl:gap-12 2xl:gap-16 px-4 sm:px-6 md:px-8 lg:px-12 xl:px-16 2xl:px-20 3xl:px-24 py-4 sm:py-6 md:py-8 lg:py-12 xl:py-16 2xl:py-20 text-left focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50 transition-all duration-200"
                aria-expanded={openItem === item.id}
              >
                {/* Icon: fixed container + tight line-height */}
                <span className="flex-shrink-0 leading-none">
                  {openItem === item.id ? (
                    <span className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl 2xl:text-7xl 3xl:text-8xl text-red-400 font-light leading-none">
                      −
                    </span>
                  ) : (
                    <span className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl 2xl:text-7xl 3xl:text-8xl text-gray-600 font-light leading-none">
                      +
                    </span>
                  )}
                </span>

                {/* Question + Answer stacked */}
                <div className="flex flex-col flex-1">
                  <span className="text-lg sm:text-xl md:text-2xl lg:text-3xl xl:text-4xl 2xl:text-5xl 3xl:text-6xl font-semibold text-[#021018] leading-tight">
                    {item.question}
                  </span>
                  {openItem === item.id && item.answer && (
                    <span className="mt-2 sm:mt-3 md:mt-4 lg:mt-6 xl:mt-8 text-base sm:text-lg md:text-xl lg:text-2xl xl:text-3xl 2xl:text-4xl 3xl:text-5xl font-light text-[#021018] leading-relaxed max-w-none lg:max-w-4xl xl:max-w-5xl 2xl:max-w-6xl">
                      {item.answer}
                    </span>
                  )}
                </div>
              </button>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default FAQSection;
